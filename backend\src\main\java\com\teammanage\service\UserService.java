package com.teammanage.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.teammanage.dto.request.UpdateUserProfileRequest;
import com.teammanage.dto.response.TeamDetailResponse;
import com.teammanage.dto.response.UserPersonalStatsResponse;
import com.teammanage.dto.response.UserProfileDetailResponse;
import com.teammanage.dto.response.UserProfileResponse;
import com.teammanage.entity.Account;
import com.teammanage.exception.BusinessException;
import com.teammanage.exception.ResourceNotFoundException;
import com.teammanage.mapper.AccountMapper;
import com.teammanage.service.TeamService;
import com.teammanage.util.PasswordUtil;

import java.util.List;

/**
 * 用户管理服务
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class UserService {

    private static final Logger log = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private PasswordUtil passwordUtil;

    @Autowired
    private TeamService teamService;

    /**
     * 根据ID获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    public Account getUserById(Long userId) {
        Account account = accountMapper.selectById(userId);
        if (account == null) {
            throw new ResourceNotFoundException("用户不存在");
        }
        return account;
    }

    /**
     * 根据邮箱获取用户信息
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    public Account getUserByEmail(String email) {
        Account account = accountMapper.findByEmail(email);
        if (account == null) {
            throw new ResourceNotFoundException("用户不存在");
        }
        return account;
    }

    /**
     * 获取用户个人资料
     * 
     * @param userId 用户ID
     * @return 用户个人资料
     */
    public UserProfileResponse getUserProfile(Long userId) {
        Account account = getUserById(userId);
        
        UserProfileResponse response = new UserProfileResponse();
        response.setId(account.getId());
        response.setEmail(account.getEmail());
        response.setName(account.getName());
        response.setDefaultSubscriptionPlanId(account.getDefaultSubscriptionPlanId());
        response.setCreatedAt(account.getCreatedAt());
        response.setUpdatedAt(account.getUpdatedAt());
        
        return response;
    }

    /**
     * 更新用户个人资料
     * 
     * @param userId 用户ID
     * @param request 更新请求
     * @return 更新后的用户资料
     */
    @Transactional
    public UserProfileResponse updateUserProfile(Long userId, UpdateUserProfileRequest request) {
        Account account = getUserById(userId);
        
        // 更新用户名
        if (StringUtils.hasText(request.getName())) {
            account.setName(request.getName());
        }
        
        // 更新密码
        if (StringUtils.hasText(request.getNewPassword())) {
            // 验证旧密码
            if (!StringUtils.hasText(request.getCurrentPassword())) {
                throw new BusinessException("修改密码时必须提供当前密码");
            }
            
            if (!passwordUtil.matches(request.getCurrentPassword(), account.getPasswordHash())) {
                throw new BusinessException("当前密码不正确");
            }
            
            // 设置新密码
            account.setPasswordHash(passwordUtil.encode(request.getNewPassword()));
        }
        
        // 更新数据库
        accountMapper.updateById(account);
        
        log.info("用户资料更新成功: userId={}, name={}", userId, account.getName());
        
        return getUserProfile(userId);
    }

    /**
     * 检查邮箱是否已存在
     * 
     * @param email 邮箱
     * @return 是否存在
     */
    public boolean isEmailExists(String email) {
        return accountMapper.existsByEmail(email);
    }

    /**
     * 验证用户密码
     *
     * @param userId 用户ID
     * @param password 密码
     * @return 是否正确
     */
    public boolean validatePassword(Long userId, String password) {
        Account account = getUserById(userId);
        return passwordUtil.matches(password, account.getPasswordHash());
    }



    /**
     * 修改用户密码
     * 
     * @param userId 用户ID
     * @param currentPassword 当前密码
     * @param newPassword 新密码
     */
    @Transactional
    public void changePassword(Long userId, String currentPassword, String newPassword) {
        Account account = getUserById(userId);
        
        // 验证当前密码
        if (!passwordUtil.matches(currentPassword, account.getPasswordHash())) {
            throw new BusinessException("当前密码不正确");
        }
        
        // 设置新密码
        account.setPasswordHash(passwordUtil.encode(newPassword));
        accountMapper.updateById(account);
        
        log.info("用户密码修改成功: userId={}", userId);
    }

    /**
     * 更新用户默认订阅套餐
     *
     * @param userId 用户ID
     * @param planId 套餐ID
     */
    @Transactional
    public void updateDefaultSubscriptionPlan(Long userId, Long planId) {
        Account account = getUserById(userId);
        account.setDefaultSubscriptionPlanId(planId);
        accountMapper.updateById(account);

        log.info("用户默认套餐更新成功: userId={}, planId={}", userId, planId);
    }

    /**
     * 获取用户详细信息
     *
     * @param userId 用户ID
     * @return 用户详细信息
     */
    public UserProfileDetailResponse getUserProfileDetail(Long userId) {
        Account account = getUserById(userId);

        // 获取用户团队统计信息
        List<TeamDetailResponse> userTeams = teamService.getUserTeams(userId);
        int teamCount = userTeams.size();

        // 获取最后登录的团队信息
        String lastLoginTeam = "暂无团队";
        if (!userTeams.isEmpty()) {
            // 简单取第一个团队作为最后登录团队，实际应该从用户会话中获取
            lastLoginTeam = userTeams.get(0).getName();
        }

        UserProfileDetailResponse response = new UserProfileDetailResponse();
        response.setName(account.getName());
        response.setPosition("团队成员"); // 可以根据用户在团队中的角色动态设置
        response.setEmail(account.getEmail());
        response.setTelephone(account.getTelephone() != null ? account.getTelephone() : "");
        response.setRegisterDate(account.getCreatedAt().toLocalDate().toString());
        response.setLastLoginTime(account.getUpdatedAt().toString());
        response.setLastLoginTeam(lastLoginTeam);
        response.setTeamCount(teamCount);
        response.setAvatar(""); // 头像URL，暂时为空

        return response;
    }

    /**
     * 获取用户个人统计数据
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 个人统计数据
     */
    public UserPersonalStatsResponse getUserPersonalStats(Long userId, Long teamId) {
        try {
            // 获取用户团队信息来计算统计数据
            List<TeamDetailResponse> userTeams = teamService.getUserTeams(userId);

            // 计算统计数据（这里使用简单的计算逻辑，实际应该从相关业务表查询）
            int vehicles = 0;
            int personnel = 0;
            int warnings = 0;
            int alerts = 0;

            // 遍历用户的团队，累计统计数据
            for (TeamDetailResponse team : userTeams) {
                if (team.getStats() != null) {
                    java.util.Map<String, Object> teamStats = team.getStats();
                    vehicles += (Integer) teamStats.getOrDefault("vehicles", 0);
                    personnel += (Integer) teamStats.getOrDefault("personnel", 0);
                    warnings += (Integer) teamStats.getOrDefault("expiring", 0);
                    alerts += (Integer) teamStats.getOrDefault("overdue", 0);
                }
            }

            return new UserPersonalStatsResponse(vehicles, personnel, warnings, alerts);
        } catch (Exception e) {
            log.error("获取用户个人统计数据失败", e);
            // 如果获取失败，返回默认的模拟数据
            return new UserPersonalStatsResponse(48, 16, 5, 3);
        }
    }

}
