{"version": 3, "sources": ["umi.8387006764061758302.hot-update.js", "src/services/user.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='16992313624391142689';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 用户管理相关 API 服务\n */\n\nimport type {\n  UpdateUserProfileRequest,\n  UserPersonalStatsResponse,\n  UserProfileDetailResponse,\n  UserProfileResponse,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\n/**\n * 用户服务类\n *\n * 提供用户相关的所有API接口，包括：\n * - 用户资料管理（查看、更新）\n * - 密码修改\n * - 用户统计信息\n * - 账户管理\n *\n * <AUTHOR>\n * @since 1.0.0\n */\nexport class UserService {\n  /**\n   * 获取当前用户资料\n   *\n   * 获取当前登录用户的基本资料信息，包括姓名、邮箱、电话等。\n   * 需要有效的用户Token。\n   *\n   * @returns Promise<UserProfileResponse> 用户资料信息\n   * @throws 当用户未登录或Token无效时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const profile = await UserService.getUserProfile();\n   * console.log('用户姓名:', profile.name);\n   * console.log('用户邮箱:', profile.email);\n   * ```\n   */\n  static async getUserProfile(): Promise<UserProfileResponse> {\n    const response =\n      await apiRequest.get<UserProfileResponse>('/users/profile');\n    return response.data;\n  }\n\n  /**\n   * 更新用户资料\n   *\n   * 更新当前用户的资料信息。可以更新姓名、电话、职位等信息。\n   * 邮箱通常不允许修改，因为它是用户的唯一标识。\n   *\n   * @param data 用户资料更新请求参数\n   * @param data.name 用户姓名\n   * @param data.telephone 电话号码（可选）\n   * @param data.position 职位（可选）\n   * @returns Promise<UserProfileResponse> 更新后的用户资料\n   * @throws 当数据验证失败或用户无权限时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const updatedProfile = await UserService.updateUserProfile({\n   *   name: '张三',\n   *   telephone: '13800138000',\n   *   position: '项目经理'\n   * });\n   * console.log('资料更新成功:', updatedProfile);\n   * ```\n   */\n  static async updateUserProfile(\n    data: UpdateUserProfileRequest,\n  ): Promise<UserProfileResponse> {\n    const response = await apiRequest.put<UserProfileResponse>(\n      '/users/profile',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 修改密码\n   *\n   * 修改当前用户的登录密码。需要提供当前密码进行验证。\n   *\n   * @param currentPassword 当前密码\n   * @param newPassword 新密码（8-20位，包含字母和数字）\n   * @returns Promise<void> 修改成功时resolve\n   * @throws 当当前密码错误或新密码格式不正确时抛出异常\n   *\n   * @example\n   * ```typescript\n   * await UserService.changePassword('oldPassword123', 'newPassword456');\n   * console.log('密码修改成功');\n   * ```\n   */\n  static async changePassword(\n    currentPassword: string,\n    newPassword: string,\n  ): Promise<void> {\n    const data: UpdateUserProfileRequest = {\n      currentPassword,\n      newPassword,\n    };\n\n    const response = await apiRequest.put<void>('/users/profile', data);\n    return response.data;\n  }\n\n  /**\n   * 更新用户名\n   */\n  static async updateUserName(name: string): Promise<UserProfileResponse> {\n    const data: UpdateUserProfileRequest = {\n      name,\n    };\n\n    const response = await apiRequest.put<UserProfileResponse>(\n      '/users/profile',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 验证当前密码\n   */\n  static async validateCurrentPassword(password: string): Promise<boolean> {\n    try {\n      const response = await apiRequest.post<boolean>(\n        '/users/validate-password',\n        { password },\n      );\n      return response.data;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * 获取用户统计信息\n   *\n   * 获取用户的团队统计信息，包括总团队数、创建的团队数等。\n   * 注意：当前返回模拟数据，等待后端实现专门的统计接口。\n   *\n   * @returns Promise<object> 用户统计信息\n   * @returns Promise<object>.totalTeams 总团队数\n   * @returns Promise<object>.createdTeams 创建的团队数\n   * @returns Promise<object>.joinedTeams 加入的团队数\n   * @returns Promise<object>.lastLoginTime 最后登录时间\n   *\n   * @example\n   * ```typescript\n   * const stats = await UserService.getUserStats();\n   * console.log('总团队数:', stats.totalTeams);\n   * console.log('创建的团队:', stats.createdTeams);\n   * ```\n   */\n  static async getUserStats(): Promise<{\n    totalTeams: number;\n    createdTeams: number;\n    joinedTeams: number;\n    lastLoginTime: string;\n  }> {\n    // 这里可能需要后端提供专门的统计接口\n    // 暂时返回模拟数据\n    return {\n      totalTeams: 0,\n      createdTeams: 0,\n      joinedTeams: 0,\n      lastLoginTime: new Date().toISOString(),\n    };\n  }\n\n  /**\n   * 获取用户个人统计数据\n   *\n   * 获取用户在当前团队中的业务统计数据，包括车辆、人员、预警、告警等数量。\n   * 这些数据用于个人中心的统计卡片显示。\n   *\n   * @returns Promise<UserPersonalStatsResponse> 个人统计数据\n   * @throws 当用户未选择团队或无权限时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const personalStats = await UserService.getUserPersonalStats();\n   * console.log('车辆数量:', personalStats.vehicles);\n   * console.log('人员数量:', personalStats.personnel);\n   * console.log('预警数量:', personalStats.warnings);\n   * console.log('告警数量:', personalStats.alerts);\n   * ```\n   */\n  static async getUserPersonalStats(): Promise<UserPersonalStatsResponse> {\n    const response = await apiRequest.get<UserPersonalStatsResponse>(\n      '/users/personal-stats',\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取用户详细信息\n   *\n   * 获取用户的详细资料信息，包括基本信息、注册时间、最后登录信息等。\n   * 比getUserProfile返回更多的详细信息，用于个人中心展示。\n   *\n   * @returns Promise<UserProfileDetailResponse> 用户详细信息\n   * @throws 当用户未登录或Token无效时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const detail = await UserService.getUserProfileDetail();\n   * console.log('注册时间:', detail.registerDate);\n   * console.log('最后登录:', detail.lastLoginTime);\n   * console.log('团队数量:', detail.teamCount);\n   * ```\n   */\n  static async getUserProfileDetail(): Promise<UserProfileDetailResponse> {\n    const response = await apiRequest.get<UserProfileDetailResponse>(\n      '/users/profile-detail',\n    );\n    return response.data;\n  }\n\n\n\n  /**\n   * 删除用户账户\n   */\n  static async deleteAccount(password: string): Promise<void> {\n    const response = await apiRequest.delete<void>('/users/account', {\n      password,\n    });\n    return response.data;\n  }\n}\n\n// 导出默认实例\nexport default UserService;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCqBA,WAAW;2BAAX;;gBAoNb,SAAS;gBACT,OAA2B;2BAA3B;;;;;4CAnO2B;;;;;;;;;YAcpB,MAAM;gBACX;;;;;;;;;;;;;;;GAeC,GACD,aAAa,iBAA+C;oBAC1D,MAAM,WACJ,MAAM,mBAAU,CAAC,GAAG,CAAsB;oBAC5C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,aAAa,kBACX,IAA8B,EACA;oBAC9B,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;GAeC,GACD,aAAa,eACX,eAAuB,EACvB,WAAmB,EACJ;oBACf,MAAM,OAAiC;wBACrC;wBACA;oBACF;oBAEA,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAO,kBAAkB;oBAC9D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,eAAe,IAAY,EAAgC;oBACtE,MAAM,OAAiC;wBACrC;oBACF;oBAEA,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,wBAAwB,QAAgB,EAAoB;oBACvE,IAAI;wBACF,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,4BACA;4BAAE;wBAAS;wBAEb,OAAO,SAAS,IAAI;oBACtB,EAAE,OAAM;wBACN,OAAO;oBACT;gBACF;gBAEA;;;;;;;;;;;;;;;;;;GAkBC,GACD,aAAa,eAKV;oBACD,oBAAoB;oBACpB,WAAW;oBACX,OAAO;wBACL,YAAY;wBACZ,cAAc;wBACd,aAAa;wBACb,eAAe,IAAI,OAAO,WAAW;oBACvC;gBACF;gBAEA;;;;;;;;;;;;;;;;;GAiBC,GACD,aAAa,uBAA2D;oBACtE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,aAAa,uBAA2D;oBACtE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAIA;;GAEC,GACD,aAAa,cAAc,QAAgB,EAAiB;oBAC1D,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CAAO,kBAAkB;wBAC/D;oBACF;oBACA,OAAO,SAAS,IAAI;gBACtB;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;ID1OD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}