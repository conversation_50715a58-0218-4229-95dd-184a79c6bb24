{"version": 3, "sources": ["umi.198964142040223682.hot-update.js", "src/services/team.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='15898784106243044659';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队管理相关 API 服务\n */\n\nimport type {\n  CreateTeamRequest,\n  InviteMembersRequest,\n  PageRequest,\n  PageResponse,\n  TeamDetailResponse,\n  TeamMemberResponse,\n  UpdateTeamRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\n/**\n * 团队服务类\n */\nexport class TeamService {\n  /**\n   * 创建团队（需要 Account Token）\n   */\n  static async createTeam(\n    data: CreateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.post<TeamDetailResponse>('/teams', data);\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表（需要 Account Token）\n   */\n  static async getUserTeams(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>('/teams');\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表（包含统计数据）\n   */\n  static async getUserTeamsWithStats(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>(\n      '/teams?includeStats=true',\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取当前团队详情（需要 Team Token）\n   */\n  static async getCurrentTeamDetail(): Promise<TeamDetailResponse> {\n    const response = await apiRequest.get<TeamDetailResponse>('/teams/current');\n    return response.data;\n  }\n\n  /**\n   * 更新当前团队信息（需要 Team Token，仅创建者）\n   */\n  static async updateCurrentTeam(\n    data: UpdateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.put<TeamDetailResponse>(\n      '/teams/current',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除当前团队（需要 Team Token，仅创建者）\n   *\n   * 权限要求：\n   * - 需要有效的Team Token\n   * - 只有团队创建者可以执行此操作\n   *\n   * 删除效果：\n   * - 软删除团队记录\n   * - 级联删除所有团队成员关系\n   * - 不可恢复\n   *\n   * @returns Promise<void> 删除成功时resolve\n   * @throws 当权限不足或团队不存在时抛出异常\n   */\n  static async deleteCurrentTeam(): Promise<void> {\n    await apiRequest.delete<string>('/teams/current');\n  }\n\n  /**\n   * 获取当前团队成员列表（需要 Team Token）\n   * 返回所有成员的简单数组格式\n   */\n  static async getCurrentTeamMembers(): Promise<TeamMemberResponse[]> {\n    const response = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000, // 获取大量数据以确保包含所有成员\n    });\n    return response?.list || [];\n  }\n\n  /**\n   * 获取当前团队成员列表（需要 Team Token）\n   */\n  static async getTeamMembers(\n    params?: PageRequest,\n  ): Promise<PageResponse<TeamMemberResponse>> {\n    const response = await apiRequest.get<PageResponse<TeamMemberResponse>>(\n      '/teams/current/members',\n      params,\n    );\n    return response.data;\n  }\n\n  /**\n   * 邀请团队成员（需要 Team Token，仅创建者）\n   */\n  static async inviteMembers(data: InviteMembersRequest): Promise<void> {\n    const response = await apiRequest.post<void>(\n      '/teams/current/members/invite',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 移除团队成员（需要 Team Token，仅创建者）\n   */\n  static async removeMember(memberId: number): Promise<void> {\n    const response = await apiRequest.delete<void>(\n      `/teams/current/members/${memberId}`,\n    );\n    return response.data;\n  }\n\n  /**\n   * 更新团队成员状态（需要 Team Token，仅创建者）\n   */\n  static async updateMemberStatus(memberId: number, isActive: boolean): Promise<void> {\n    const response = await apiRequest.put<void>(\n      `/teams/current/members/${memberId}/status?isActive=${isActive}`,\n    );\n    return response.data;\n  }\n\n\n\n  /**\n   * 获取团队统计信息\n   */\n  static async getTeamStats(): Promise<{\n    memberCount: number;\n    activeMembers: number;\n    recentActivity: number;\n  }> {\n    // 这里可能需要后端提供专门的统计接口\n    // 暂时通过团队详情和成员列表来计算\n    const teamDetail = await TeamService.getCurrentTeamDetail();\n    const members = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000,\n    });\n\n    const activeMembers = members.list.filter(\n      (member) => member.isActive,\n    ).length;\n    const recentActivity = members.list.filter((member) => {\n      const lastAccess = new Date(member.lastAccessTime);\n      const weekAgo = new Date();\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      return lastAccess > weekAgo;\n    }).length;\n\n    return {\n      memberCount: teamDetail.memberCount,\n      activeMembers,\n      recentActivity,\n    };\n  }\n\n\n}\n\n// 导出默认实例\nexport default TeamService;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCeA,WAAW;2BAAX;;gBAmKb,SAAS;gBACT,OAA2B;2BAA3B;;;;;4CAzK2B;;;;;;;;;YAKpB,MAAM;gBACX;;GAEC,GACD,aAAa,WACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAqB,UAAU;oBACrE,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,eAA8C;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAuB;oBAC5D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,uBAAoD;oBAC/D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAqB;oBAC1D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,kBACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;GAcC,GACD,aAAa,oBAAmC;oBAC9C,MAAM,mBAAU,CAAC,MAAM,CAAS;gBAClC;gBAEA;;;GAGC,GACD,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,YAAY,cAAc,CAAC;wBAChD,SAAS;wBACT,UAAU;oBACZ;oBACA,OAAO,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;gBAC7B;gBAEA;;GAEC,GACD,aAAa,eACX,MAAoB,EACuB;oBAC3C,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,0BACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,cAAc,IAA0B,EAAiB;oBACpE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,iCACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,aAAa,QAAgB,EAAiB;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CACtC,CAAC,uBAAuB,EAAE,SAAS,CAAC;oBAEtC,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,mBAAmB,QAAgB,EAAE,QAAiB,EAAiB;oBAClF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,uBAAuB,EAAE,SAAS,iBAAiB,EAAE,SAAS,CAAC;oBAElE,OAAO,SAAS,IAAI;gBACtB;gBAIA;;GAEC,GACD,aAAa,eAIV;oBACD,oBAAoB;oBACpB,mBAAmB;oBACnB,MAAM,aAAa,MAAM,YAAY,oBAAoB;oBACzD,MAAM,UAAU,MAAM,YAAY,cAAc,CAAC;wBAC/C,SAAS;wBACT,UAAU;oBACZ;oBAEA,MAAM,gBAAgB,QAAQ,IAAI,CAAC,MAAM,CACvC,CAAC,SAAW,OAAO,QAAQ,EAC3B,MAAM;oBACR,MAAM,iBAAiB,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC1C,MAAM,aAAa,IAAI,KAAK,OAAO,cAAc;wBACjD,MAAM,UAAU,IAAI;wBACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;wBACpC,OAAO,aAAa;oBACtB,GAAG,MAAM;oBAET,OAAO;wBACL,aAAa,WAAW,WAAW;wBACnC;wBACA;oBACF;gBACF;YAGF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IDnLD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}