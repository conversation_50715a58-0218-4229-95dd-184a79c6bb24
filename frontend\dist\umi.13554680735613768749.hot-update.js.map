{"version": 3, "sources": ["umi.13554680735613768749.hot-update.js", "src/services/team.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='2174938121338765813';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 团队管理相关 API 服务\n */\n\nimport type {\n  CreateTeamRequest,\n  InviteMembersRequest,\n  PageRequest,\n  PageResponse,\n  TeamDetailResponse,\n  TeamMemberResponse,\n  UpdateTeamRequest,\n} from '@/types/api';\nimport { apiRequest } from '@/utils/request';\n\n/**\n * 团队服务类\n *\n * 提供团队相关的所有API接口，包括：\n * - 团队创建和管理\n * - 团队成员管理\n * - 团队邀请功能\n * - 团队统计信息\n *\n * 权限说明：\n * - Account Token：用户级别操作（创建团队、获取用户团队列表）\n * - Team Token：团队级别操作（团队详情、成员管理等）\n * - 创建者权限：某些操作仅团队创建者可执行\n *\n * <AUTHOR>\n * @since 1.0.0\n */\nexport class TeamService {\n  /**\n   * 创建团队\n   *\n   * 创建新的团队，创建者自动成为团队管理员。\n   * 需要用户级别的Token（Account Token）。\n   *\n   * @param data 团队创建请求参数\n   * @param data.name 团队名称（必填，2-50字符）\n   * @param data.description 团队描述（可选）\n   * @returns Promise<TeamDetailResponse> 创建的团队信息\n   * @throws 当团队名称重复或用户权限不足时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const newTeam = await TeamService.createTeam({\n   *   name: '开发团队',\n   *   description: '负责产品开发的团队'\n   * });\n   * console.log('团队创建成功:', newTeam.name);\n   * ```\n   */\n  static async createTeam(\n    data: CreateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.post<TeamDetailResponse>('/teams', data);\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表\n   *\n   * 获取当前用户所属的所有团队的基本信息。\n   * 需要用户级别的Token（Account Token）。\n   *\n   * @returns Promise<TeamDetailResponse[]> 团队列表\n   * @throws 当用户未登录时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teams = await TeamService.getUserTeams();\n   * console.log('用户所属团队数量:', teams.length);\n   * teams.forEach(team => {\n   *   console.log(`团队: ${team.name}, ID: ${team.id}`);\n   * });\n   * ```\n   */\n  static async getUserTeams(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>('/teams');\n    return response.data;\n  }\n\n  /**\n   * 获取用户的团队列表（包含统计数据）\n   *\n   * 获取当前用户所属的所有团队，包含每个团队的统计信息。\n   * 用于个人中心的团队列表展示。\n   *\n   * @returns Promise<TeamDetailResponse[]> 带统计信息的团队列表\n   * @throws 当用户未登录时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teams = await TeamService.getUserTeamsWithStats();\n   * teams.forEach(team => {\n   *   console.log(`团队: ${team.name}, 成员数: ${team.memberCount}`);\n   * });\n   * ```\n   */\n  static async getUserTeamsWithStats(): Promise<TeamDetailResponse[]> {\n    const response = await apiRequest.get<TeamDetailResponse[]>(\n      '/teams?includeStats=true',\n    );\n    return response.data;\n  }\n\n  /**\n   * 获取当前团队详情\n   *\n   * 获取当前选择团队的详细信息。\n   * 需要团队级别的Token（Team Token）。\n   *\n   * @returns Promise<TeamDetailResponse> 团队详细信息\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const teamDetail = await TeamService.getCurrentTeamDetail();\n   * console.log('当前团队:', teamDetail.name);\n   * console.log('团队描述:', teamDetail.description);\n   * console.log('成员数量:', teamDetail.memberCount);\n   * ```\n   */\n  static async getCurrentTeamDetail(): Promise<TeamDetailResponse> {\n    const response = await apiRequest.get<TeamDetailResponse>('/teams/current');\n    return response.data;\n  }\n\n  /**\n   * 更新当前团队信息\n   *\n   * 更新当前团队的基本信息，如名称、描述等。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param data 团队更新请求参数\n   * @param data.name 新的团队名称（可选）\n   * @param data.description 新的团队描述（可选）\n   * @returns Promise<TeamDetailResponse> 更新后的团队信息\n   * @throws 当用户非团队创建者或团队名称重复时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const updatedTeam = await TeamService.updateCurrentTeam({\n   *   name: '新团队名称',\n   *   description: '更新后的团队描述'\n   * });\n   * console.log('团队信息更新成功');\n   * ```\n   */\n  static async updateCurrentTeam(\n    data: UpdateTeamRequest,\n  ): Promise<TeamDetailResponse> {\n    const response = await apiRequest.put<TeamDetailResponse>(\n      '/teams/current',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 删除当前团队（需要 Team Token，仅创建者）\n   *\n   * 权限要求：\n   * - 需要有效的Team Token\n   * - 只有团队创建者可以执行此操作\n   *\n   * 删除效果：\n   * - 软删除团队记录\n   * - 级联删除所有团队成员关系\n   * - 不可恢复\n   *\n   * @returns Promise<void> 删除成功时resolve\n   * @throws 当权限不足或团队不存在时抛出异常\n   */\n  static async deleteCurrentTeam(): Promise<void> {\n    await apiRequest.delete<string>('/teams/current');\n  }\n\n  /**\n   * 获取当前团队成员列表（简单数组格式）\n   *\n   * 获取当前团队的所有成员，返回简单数组格式。\n   * 内部调用分页接口并获取所有成员。\n   *\n   * @returns Promise<TeamMemberResponse[]> 团队成员列表\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const members = await TeamService.getCurrentTeamMembers();\n   * console.log('团队成员数量:', members.length);\n   * members.forEach(member => {\n   *   console.log(`成员: ${member.name}, 邮箱: ${member.email}`);\n   * });\n   * ```\n   */\n  static async getCurrentTeamMembers(): Promise<TeamMemberResponse[]> {\n    const response = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000, // 获取大量数据以确保包含所有成员\n    });\n    return response?.list || [];\n  }\n\n  /**\n   * 获取当前团队成员列表（分页格式）\n   *\n   * 获取当前团队的成员列表，支持分页查询。\n   * 需要团队级别的Token（Team Token）。\n   *\n   * @param params 分页查询参数（可选）\n   * @param params.current 当前页码（默认1）\n   * @param params.pageSize 每页大小（默认10）\n   * @returns Promise<PageResponse<TeamMemberResponse>> 分页的成员列表\n   * @throws 当未选择团队或无权限访问时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const membersPage = await TeamService.getTeamMembers({\n   *   current: 1,\n   *   pageSize: 20\n   * });\n   *\n   * console.log('总成员数:', membersPage.total);\n   * console.log('当前页成员:', membersPage.list);\n   * ```\n   */\n  static async getTeamMembers(\n    params?: PageRequest,\n  ): Promise<PageResponse<TeamMemberResponse>> {\n    const response = await apiRequest.get<PageResponse<TeamMemberResponse>>(\n      '/teams/current/members',\n      params,\n    );\n    return response.data;\n  }\n\n  /**\n   * 邀请团队成员\n   *\n   * 向指定邮箱发送团队邀请。被邀请人会收到邮件邀请链接。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param data 邀请请求参数\n   * @param data.emails 被邀请人的邮箱列表\n   * @returns Promise<void> 邀请发送成功时resolve\n   * @throws 当用户非团队创建者或邮箱格式错误时抛出异常\n   *\n   * @example\n   * ```typescript\n   * await TeamService.inviteMembers({\n   *   emails: ['<EMAIL>', '<EMAIL>']\n   * });\n   * console.log('邀请已发送');\n   * ```\n   */\n  static async inviteMembers(data: InviteMembersRequest): Promise<void> {\n    const response = await apiRequest.post<void>(\n      '/teams/current/members/invite',\n      data,\n    );\n    return response.data;\n  }\n\n  /**\n   * 移除团队成员\n   *\n   * 从当前团队中移除指定成员。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param memberId 要移除的成员ID\n   * @returns Promise<void> 移除成功时resolve\n   * @throws 当用户非团队创建者或成员不存在时抛出异常\n   *\n   * @example\n   * ```typescript\n   * await TeamService.removeMember(123);\n   * console.log('成员已移除');\n   * ```\n   */\n  static async removeMember(memberId: number): Promise<void> {\n    const response = await apiRequest.delete<void>(\n      `/teams/current/members/${memberId}`,\n    );\n    return response.data;\n  }\n\n  /**\n   * 更新团队成员状态\n   *\n   * 更新团队成员的激活状态（启用/禁用）。\n   * 需要团队级别的Token，且仅团队创建者有权限。\n   *\n   * @param memberId 成员ID\n   * @param isActive 是否激活（true=启用，false=禁用）\n   * @returns Promise<void> 更新成功时resolve\n   * @throws 当用户非团队创建者或成员不存在时抛出异常\n   *\n   * @example\n   * ```typescript\n   * // 禁用成员\n   * await TeamService.updateMemberStatus(123, false);\n   * console.log('成员已禁用');\n   *\n   * // 启用成员\n   * await TeamService.updateMemberStatus(123, true);\n   * console.log('成员已启用');\n   * ```\n   */\n  static async updateMemberStatus(memberId: number, isActive: boolean): Promise<void> {\n    const response = await apiRequest.put<void>(\n      `/teams/current/members/${memberId}/status?isActive=${isActive}`,\n    );\n    return response.data;\n  }\n\n\n\n  /**\n   * 获取团队统计信息\n   */\n  static async getTeamStats(): Promise<{\n    memberCount: number;\n    activeMembers: number;\n    recentActivity: number;\n  }> {\n    // 这里可能需要后端提供专门的统计接口\n    // 暂时通过团队详情和成员列表来计算\n    const teamDetail = await TeamService.getCurrentTeamDetail();\n    const members = await TeamService.getTeamMembers({\n      current: 1,\n      pageSize: 1000,\n    });\n\n    const activeMembers = members.list.filter(\n      (member) => member.isActive,\n    ).length;\n    const recentActivity = members.list.filter((member) => {\n      const lastAccess = new Date(member.lastAccessTime);\n      const weekAgo = new Date();\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      return lastAccess > weekAgo;\n    }).length;\n\n    return {\n      memberCount: teamDetail.memberCount,\n      activeMembers,\n      recentActivity,\n    };\n  }\n\n\n}\n\n// 导出默认实例\nexport default TeamService;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBC6BA,WAAW;2BAAX;;gBAoUb,SAAS;gBACT,OAA2B;2BAA3B;;;;;4CAxV2B;;;;;;;;;YAmBpB,MAAM;gBACX;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,aAAa,WACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAqB,UAAU;oBACrE,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;GAiBC,GACD,aAAa,eAA8C;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAuB;oBAC5D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,aAAa,uBAAoD;oBAC/D,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAqB;oBAC1D,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;;;GAoBC,GACD,aAAa,kBACX,IAAuB,EACM;oBAC7B,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,kBACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;GAcC,GACD,aAAa,oBAAmC;oBAC9C,MAAM,mBAAU,CAAC,MAAM,CAAS;gBAClC;gBAEA;;;;;;;;;;;;;;;;;GAiBC,GACD,aAAa,wBAAuD;oBAClE,MAAM,WAAW,MAAM,YAAY,cAAc,CAAC;wBAChD,SAAS;wBACT,UAAU;oBACZ;oBACA,OAAO,CAAA,qBAAA,+BAAA,SAAU,IAAI,KAAI,EAAE;gBAC7B;gBAEA;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,aAAa,eACX,MAAoB,EACuB;oBAC3C,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,0BACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;GAkBC,GACD,aAAa,cAAc,IAA0B,EAAiB;oBACpE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,iCACA;oBAEF,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;GAeC,GACD,aAAa,aAAa,QAAgB,EAAiB;oBACzD,MAAM,WAAW,MAAM,mBAAU,CAAC,MAAM,CACtC,CAAC,uBAAuB,EAAE,SAAS,CAAC;oBAEtC,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;;;;GAqBC,GACD,aAAa,mBAAmB,QAAgB,EAAE,QAAiB,EAAiB;oBAClF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CACnC,CAAC,uBAAuB,EAAE,SAAS,iBAAiB,EAAE,SAAS,CAAC;oBAElE,OAAO,SAAS,IAAI;gBACtB;gBAIA;;GAEC,GACD,aAAa,eAIV;oBACD,oBAAoB;oBACpB,mBAAmB;oBACnB,MAAM,aAAa,MAAM,YAAY,oBAAoB;oBACzD,MAAM,UAAU,MAAM,YAAY,cAAc,CAAC;wBAC/C,SAAS;wBACT,UAAU;oBACZ;oBAEA,MAAM,gBAAgB,QAAQ,IAAI,CAAC,MAAM,CACvC,CAAC,SAAW,OAAO,QAAQ,EAC3B,MAAM;oBACR,MAAM,iBAAiB,QAAQ,IAAI,CAAC,MAAM,CAAC,CAAC;wBAC1C,MAAM,aAAa,IAAI,KAAK,OAAO,cAAc;wBACjD,MAAM,UAAU,IAAI;wBACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;wBACpC,OAAO,aAAa;oBACtB,GAAG,MAAM;oBAET,OAAO;wBACL,aAAa,WAAW,WAAW;wBACnC;wBACA;oBACF;gBACF;YAGF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IDlWD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}