globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/services/auth.ts": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                AuthService: function() {
                    return AuthService;
                },
                // 导出默认实例
                default: function() {
                    return _default;
                }
            });
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _request = __mako_require__("src/utils/request.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            class AuthService {
                /**
   * 发送验证码
   *
   * 向指定邮箱发送6位数字验证码，用于登录验证。
   * 如果邮箱未注册，系统会在登录时自动创建新用户。
   *
   * @param data 验证码发送请求参数
   * @param data.email 接收验证码的邮箱地址
   * @returns Promise<SendVerificationCodeResponse> 发送结果，包含成功状态和调试信息
   * @throws 当邮箱格式错误或发送频率过高时抛出异常
   *
   * @example
   * ```typescript
   * const response = await AuthService.sendVerificationCode({
   *   email: '<EMAIL>'
   * });
   *
   * if (response.success) {
   *   console.log('验证码发送成功');
   *   // 在开发环境中，验证码会自动填充到输入框
   * }
   * ```
   */ static async sendVerificationCode(data) {
                    const response = await _request.apiRequest.post('/auth/send-code', data);
                    // 在开发环境中输出验证码到控制台（用于调试）
                    if (response.data.success) {
                        if (response.data.debugCode) {
                            console.log('验证码:', response.data.debugCode);
                            // 提供快速填充功能
                            console.log('快速填充验证码: window.devHelper?.quickFillVerificationCode("' + response.data.debugCode + '")');
                            // 自动填充验证码（延迟1秒，确保用户能看到控制台输出）
                            setTimeout(()=>{
                                const codeInput = document.querySelector('input[placeholder*="验证码"]');
                                if (codeInput) {
                                    codeInput.value = response.data.debugCode;
                                    codeInput.dispatchEvent(new Event('input', {
                                        bubbles: true
                                    }));
                                    codeInput.dispatchEvent(new Event('change', {
                                        bubbles: true
                                    }));
                                    console.log('✅ 验证码已自动填充到输入框');
                                }
                            }, 1000);
                        } else console.log('验证码发送成功，请查看后端日志获取验证码');
                    }
                    return response.data;
                }
                // 注册功能已移除，统一使用验证码登录/注册流程
                /**
   * 用户登录（验证码登录）
   *
   * 使用邮箱和验证码进行登录。如果邮箱未注册，系统会自动创建新用户。
   * 登录成功后返回用户信息和可选择的团队列表。
   *
   * @param data 登录请求参数
   * @param data.email 用户邮箱
   * @param data.code 6位数字验证码
   * @returns Promise<LoginResponse> 登录响应，包含用户信息、Token和团队列表
   * @throws 当验证码错误、已过期或邮箱格式错误时抛出异常
   *
   * @example
   * ```typescript
   * const loginResponse = await AuthService.login({
   *   email: '<EMAIL>',
   *   code: '123456'
   * });
   *
   * console.log('用户信息:', loginResponse.user);
   * console.log('可选团队:', loginResponse.teams);
   *
   * if (loginResponse.teams.length > 0) {
   *   // 需要选择团队
   *   await AuthService.selectTeam({ teamId: loginResponse.teams[0].id });
   * }
   * ```
   */ static async login(data) {
                    const response = await _request.apiRequest.post('/auth/login', data);
                    // 保存用户Token
                    if (response.data.token) _request.TokenManager.setToken(response.data.token);
                    return response.data;
                }
                /**
   * 刷新Token
   *
   * 使用当前有效的Token获取新的Token，延长会话时间。
   * 通常在Token即将过期时自动调用。
   *
   * @returns Promise<LoginResponse> 包含新Token和用户信息的响应
   * @throws 当当前Token无效或已过期时抛出异常
   *
   * @example
   * ```typescript
   * try {
   *   const refreshResponse = await AuthService.refreshToken();
   *   console.log('Token已刷新');
   * } catch (error) {
   *   console.log('Token刷新失败，需要重新登录');
   *   // 跳转到登录页面
   * }
   * ```
   */ static async refreshToken() {
                    const response = await _request.apiRequest.post('/auth/refresh-token');
                    // 更新Token
                    if (response.data.token) _request.TokenManager.setToken(response.data.token);
                    return response.data;
                }
                /**
   * 用户登出
   *
   * 清除服务器端的会话信息并清除本地Token。
   * 即使服务器请求失败，也会清除本地Token确保用户能够重新登录。
   *
   * @returns Promise<void> 登出完成时resolve
   *
   * @example
   * ```typescript
   * await AuthService.logout();
   * console.log('已登出');
   * // 页面会自动跳转到登录页面
   * ```
   */ static async logout() {
                    try {
                        await _request.apiRequest.post('/auth/logout');
                    } finally{
                        // 无论请求是否成功，都清除本地 token
                        _request.TokenManager.clearToken();
                    }
                }
                /**
   * 验证Token有效性
   *
   * 向服务器验证当前Token是否仍然有效。
   * 用于检查用户会话状态，通常在应用启动时调用。
   *
   * @returns Promise<boolean> Token是否有效
   *
   * @example
   * ```typescript
   * const isValid = await AuthService.validateToken();
   * if (!isValid) {
   *   // Token无效，跳转到登录页面
   *   history.push('/user/login');
   * }
   * ```
   */ static async validateToken() {
                    try {
                        const response = await _request.apiRequest.get('/auth/validate');
                        return response.data;
                    } catch  {
                        return false;
                    }
                }
                /**
   * 检查是否已登录
   *
   * 检查本地是否存储了有效的Token。
   * 注意：这只检查本地存储，不验证Token的服务器端有效性。
   *
   * @returns boolean 是否已登录（本地Token存在）
   *
   * @example
   * ```typescript
   * if (AuthService.isLoggedIn()) {
   *   console.log('用户已登录');
   * } else {
   *   console.log('用户未登录');
   * }
   * ```
   */ static isLoggedIn() {
                    return _request.TokenManager.hasToken();
                }
                /**
   * 获取当前Token
   *
   * 从本地存储获取当前用户的Token。
   *
   * @returns string | null 当前Token，如果未登录则返回null
   *
   * @example
   * ```typescript
   * const token = AuthService.getToken();
   * if (token) {
   *   console.log('当前Token:', token);
   * }
   * ```
   */ static getToken() {
                    return _request.TokenManager.getToken();
                }
                /**
   * 清除Token
   *
   * 清除本地存储的Token，但不通知服务器。
   * 通常用于强制登出或Token过期处理。
   *
   * @example
   * ```typescript
   * AuthService.clearToken();
   * console.log('Token已清除');
   * ```
   */ static clearToken() {
                    _request.TokenManager.clearToken();
                }
                /**
   * 清除团队Token（兼容性方法）
   */ static clearTeamToken() {
                    // 在单令牌系统中，清除Token即可
                    _request.TokenManager.clearToken();
                }
                /**
   * 选择团队
   */ static async selectTeam(data) {
                    const response = await _request.apiRequest.post('/auth/select-team', data);
                    // 更新Token
                    if (response.data.token) _request.TokenManager.setToken(response.data.token);
                    return response.data;
                }
                /**
   * 切换团队
   */ static async switchTeam(data) {
                    const response = await _request.apiRequest.post('/auth/switch-team', data);
                    // 更新Token
                    if (response.data.token) _request.TokenManager.setToken(response.data.token);
                    return response.data;
                }
                /**
   * 清除团队上下文
   */ static async clearTeam() {
                    const response = await _request.apiRequest.post('/auth/clear-team');
                    // 更新Token
                    if (response.data) _request.TokenManager.setToken(response.data);
                    return response.data;
                }
                // ========== 兼容性方法 ==========
                /**
   * 检查是否已选择团队（兼容性方法）
   * @deprecated 在单令牌系统中，团队信息包含在Token中，使用 isLoggedIn 检查登录状态
   */ static hasTeamSelected() {
                    return AuthService.isLoggedIn();
                }
                /**
   * 团队登录（兼容性方法）
   * @deprecated 使用 selectTeam 替代
   */ static async teamLogin(data) {
                    return AuthService.selectTeam(data);
                }
                /**
   * 清除所有Token（兼容性方法）
   * @deprecated 使用 clearToken 替代
   */ static clearTokens() {
                    AuthService.clearToken();
                }
            }
            var _default = AuthService;
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '17396879651123970744';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.6071599876754058083.hot-update.js.map