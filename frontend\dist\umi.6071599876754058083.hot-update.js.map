{"version": 3, "sources": ["umi.6071599876754058083.hot-update.js", "src/services/auth.ts"], "sourcesContent": ["globalThis.makoModuleHotUpdate(\r\n  'src/.umi/umi.ts?hmr',\r\n  {\r\n    modules: {},\r\n  },\r\n  function (runtime) {\r\n    runtime._h='17396879651123970744';\nruntime.updateEnsure2Map({\"src/.umi/core/EmptyRoute.tsx\":[\"src/.umi/core/EmptyRoute.tsx\"],\"src/.umi/plugin-layout/Layout.tsx\":[\"vendors\",\"src/.umi/plugin-layout/Layout.tsx\"],\"src/pages/404.tsx\":[\"p__404\"],\"src/pages/Dashboard/index.tsx\":[\"p__Dashboard__index\"],\"src/pages/help/index.tsx\":[\"p__help__index\"],\"src/pages/invite/[token].tsx\":[\"common\",\"p__invite__token\"],\"src/pages/personal-center/index.tsx\":[\"common\",\"src/pages/personal-center/index.tsx\"],\"src/pages/subscription/index.tsx\":[\"p__subscription__index\"],\"src/pages/team-management/index.tsx\":[\"common\",\"src/pages/team-management/index.tsx\"],\"src/pages/team/detail/index.tsx\":[\"p__team__detail__index\"],\"src/pages/team/index.tsx\":[\"common\",\"p__team__index\"],\"src/pages/user/index.tsx\":[\"p__user__index\"],\"src/pages/user/invitations/index.tsx\":[\"common\",\"p__user__invitations__index\"],\"src/pages/user/login/index.tsx\":[\"common\",\"p__user__login__index\"]});;\r\n  },\r\n);\r\n", "/**\n * 认证相关 API 服务\n */\n\nimport type {\n  LoginRequest,\n  LoginResponse,\n  SendVerificationCodeRequest,\n  SendVerificationCodeResponse\n} from '@/types/api';\nimport { apiRequest, TokenManager } from '@/utils/request';\n\n/**\n * 认证服务类（验证码登录系统）\n *\n * 提供基于验证码的用户认证系统，支持：\n * - 验证码发送和验证\n * - 用户登录（自动注册新用户）\n * - 团队选择和切换\n * - Token管理和刷新\n * - 用户登出和会话管理\n *\n * <AUTHOR>\n * @since 1.0.0\n */\nexport class AuthService {\n  /**\n   * 发送验证码\n   *\n   * 向指定邮箱发送6位数字验证码，用于登录验证。\n   * 如果邮箱未注册，系统会在登录时自动创建新用户。\n   *\n   * @param data 验证码发送请求参数\n   * @param data.email 接收验证码的邮箱地址\n   * @returns Promise<SendVerificationCodeResponse> 发送结果，包含成功状态和调试信息\n   * @throws 当邮箱格式错误或发送频率过高时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const response = await AuthService.sendVerificationCode({\n   *   email: '<EMAIL>'\n   * });\n   *\n   * if (response.success) {\n   *   console.log('验证码发送成功');\n   *   // 在开发环境中，验证码会自动填充到输入框\n   * }\n   * ```\n   */\n  static async sendVerificationCode(data: SendVerificationCodeRequest): Promise<SendVerificationCodeResponse> {\n    const response = await apiRequest.post<SendVerificationCodeResponse>('/auth/send-code', data);\n\n    // 在开发环境中输出验证码到控制台（用于调试）\n    if (process.env.NODE_ENV === 'development' && response.data.success) {\n      if (response.data.debugCode) {\n        console.log('验证码:', response.data.debugCode);\n\n        // 提供快速填充功能\n        console.log('快速填充验证码: window.devHelper?.quickFillVerificationCode(\"' + response.data.debugCode + '\")');\n\n        // 自动填充验证码（延迟1秒，确保用户能看到控制台输出）\n        setTimeout(() => {\n          const codeInput = document.querySelector('input[placeholder*=\"验证码\"]') as HTMLInputElement;\n          if (codeInput) {\n            codeInput.value = response.data.debugCode!;\n            codeInput.dispatchEvent(new Event('input', { bubbles: true }));\n            codeInput.dispatchEvent(new Event('change', { bubbles: true }));\n            console.log('✅ 验证码已自动填充到输入框');\n          }\n        }, 1000);\n      } else {\n        console.log('验证码发送成功，请查看后端日志获取验证码');\n      }\n    }\n\n    return response.data;\n  }\n\n  // 注册功能已移除，统一使用验证码登录/注册流程\n\n  /**\n   * 用户登录（验证码登录）\n   *\n   * 使用邮箱和验证码进行登录。如果邮箱未注册，系统会自动创建新用户。\n   * 登录成功后返回用户信息和可选择的团队列表。\n   *\n   * @param data 登录请求参数\n   * @param data.email 用户邮箱\n   * @param data.code 6位数字验证码\n   * @returns Promise<LoginResponse> 登录响应，包含用户信息、Token和团队列表\n   * @throws 当验证码错误、已过期或邮箱格式错误时抛出异常\n   *\n   * @example\n   * ```typescript\n   * const loginResponse = await AuthService.login({\n   *   email: '<EMAIL>',\n   *   code: '123456'\n   * });\n   *\n   * console.log('用户信息:', loginResponse.user);\n   * console.log('可选团队:', loginResponse.teams);\n   *\n   * if (loginResponse.teams.length > 0) {\n   *   // 需要选择团队\n   *   await AuthService.selectTeam({ teamId: loginResponse.teams[0].id });\n   * }\n   * ```\n   */\n  static async login(data: LoginRequest): Promise<LoginResponse> {\n    const response = await apiRequest.post<LoginResponse>('/auth/login', data);\n\n    // 保存用户Token\n    if (response.data.token) {\n      TokenManager.setToken(response.data.token);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 刷新Token\n   *\n   * 使用当前有效的Token获取新的Token，延长会话时间。\n   * 通常在Token即将过期时自动调用。\n   *\n   * @returns Promise<LoginResponse> 包含新Token和用户信息的响应\n   * @throws 当当前Token无效或已过期时抛出异常\n   *\n   * @example\n   * ```typescript\n   * try {\n   *   const refreshResponse = await AuthService.refreshToken();\n   *   console.log('Token已刷新');\n   * } catch (error) {\n   *   console.log('Token刷新失败，需要重新登录');\n   *   // 跳转到登录页面\n   * }\n   * ```\n   */\n  static async refreshToken(): Promise<LoginResponse> {\n    const response = await apiRequest.post<LoginResponse>(\n      '/auth/refresh-token',\n    );\n\n    // 更新Token\n    if (response.data.token) {\n      TokenManager.setToken(response.data.token);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 用户登出\n   *\n   * 清除服务器端的会话信息并清除本地Token。\n   * 即使服务器请求失败，也会清除本地Token确保用户能够重新登录。\n   *\n   * @returns Promise<void> 登出完成时resolve\n   *\n   * @example\n   * ```typescript\n   * await AuthService.logout();\n   * console.log('已登出');\n   * // 页面会自动跳转到登录页面\n   * ```\n   */\n  static async logout(): Promise<void> {\n    try {\n      await apiRequest.post<void>('/auth/logout');\n    } finally {\n      // 无论请求是否成功，都清除本地 token\n      TokenManager.clearToken();\n    }\n  }\n\n  /**\n   * 验证Token有效性\n   *\n   * 向服务器验证当前Token是否仍然有效。\n   * 用于检查用户会话状态，通常在应用启动时调用。\n   *\n   * @returns Promise<boolean> Token是否有效\n   *\n   * @example\n   * ```typescript\n   * const isValid = await AuthService.validateToken();\n   * if (!isValid) {\n   *   // Token无效，跳转到登录页面\n   *   history.push('/user/login');\n   * }\n   * ```\n   */\n  static async validateToken(): Promise<boolean> {\n    try {\n      const response = await apiRequest.get<boolean>('/auth/validate');\n      return response.data;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * 检查是否已登录\n   *\n   * 检查本地是否存储了有效的Token。\n   * 注意：这只检查本地存储，不验证Token的服务器端有效性。\n   *\n   * @returns boolean 是否已登录（本地Token存在）\n   *\n   * @example\n   * ```typescript\n   * if (AuthService.isLoggedIn()) {\n   *   console.log('用户已登录');\n   * } else {\n   *   console.log('用户未登录');\n   * }\n   * ```\n   */\n  static isLoggedIn(): boolean {\n    return TokenManager.hasToken();\n  }\n\n  /**\n   * 获取当前Token\n   *\n   * 从本地存储获取当前用户的Token。\n   *\n   * @returns string | null 当前Token，如果未登录则返回null\n   *\n   * @example\n   * ```typescript\n   * const token = AuthService.getToken();\n   * if (token) {\n   *   console.log('当前Token:', token);\n   * }\n   * ```\n   */\n  static getToken(): string | null {\n    return TokenManager.getToken();\n  }\n\n  /**\n   * 清除Token\n   *\n   * 清除本地存储的Token，但不通知服务器。\n   * 通常用于强制登出或Token过期处理。\n   *\n   * @example\n   * ```typescript\n   * AuthService.clearToken();\n   * console.log('Token已清除');\n   * ```\n   */\n  static clearToken(): void {\n    TokenManager.clearToken();\n  }\n\n  /**\n   * 清除团队Token（兼容性方法）\n   */\n  static clearTeamToken(): void {\n    // 在单令牌系统中，清除Token即可\n    TokenManager.clearToken();\n  }\n\n  /**\n   * 选择团队\n   */\n  static async selectTeam(data: { teamId: number }): Promise<LoginResponse> {\n    const response = await apiRequest.post<LoginResponse>(\n      '/auth/select-team',\n      data,\n    );\n\n    // 更新Token\n    if (response.data.token) {\n      TokenManager.setToken(response.data.token);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 切换团队\n   */\n  static async switchTeam(data: { teamId: number }): Promise<LoginResponse> {\n    const response = await apiRequest.post<LoginResponse>(\n      '/auth/switch-team',\n      data,\n    );\n\n    // 更新Token\n    if (response.data.token) {\n      TokenManager.setToken(response.data.token);\n    }\n\n    return response.data;\n  }\n\n  /**\n   * 清除团队上下文\n   */\n  static async clearTeam(): Promise<string> {\n    const response = await apiRequest.post<string>('/auth/clear-team');\n\n    // 更新Token\n    if (response.data) {\n      TokenManager.setToken(response.data);\n    }\n\n    return response.data;\n  }\n\n  // ========== 兼容性方法 ==========\n\n  /**\n   * 检查是否已选择团队（兼容性方法）\n   * @deprecated 在单令牌系统中，团队信息包含在Token中，使用 isLoggedIn 检查登录状态\n   */\n  static hasTeamSelected(): boolean {\n    return AuthService.isLoggedIn();\n  }\n  /**\n   * 团队登录（兼容性方法）\n   * @deprecated 使用 selectTeam 替代\n   */\n  static async teamLogin(data: { teamId: number }): Promise<LoginResponse> {\n    return AuthService.selectTeam(data);\n  }\n\n  /**\n   * 清除所有Token（兼容性方法）\n   * @deprecated 使用 clearToken 替代\n   */\n  static clearTokens(): void {\n    AuthService.clearToken();\n  }\n}\n\n// 导出默认实例\nexport default AuthService;\n"], "names": [], "mappings": "AAAA,WAAW,mBAAmB,CAC5B,uBACA;IACE,SAAS;;;;;;;;;;;;;gBCsBA,WAAW;2BAAX;;gBA2Tb,SAAS;gBACT,OAA2B;2BAA3B;;;;;4CA3UyC;;;;;;;;;YAelC,MAAM;gBACX;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,aAAa,qBAAqB,IAAiC,EAAyC;oBAC1G,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAA+B,mBAAmB;oBAExF,wBAAwB;oBACxB,IAA8C,SAAS,IAAI,CAAC,OAAO;wBACjE,IAAI,SAAS,IAAI,CAAC,SAAS,EAAE;4BAC3B,QAAQ,GAAG,CAAC,QAAQ,SAAS,IAAI,CAAC,SAAS;4BAE3C,WAAW;4BACX,QAAQ,GAAG,CAAC,2DAA2D,SAAS,IAAI,CAAC,SAAS,GAAG;4BAEjG,6BAA6B;4BAC7B,WAAW;gCACT,MAAM,YAAY,SAAS,aAAa,CAAC;gCACzC,IAAI,WAAW;oCACb,UAAU,KAAK,GAAG,SAAS,IAAI,CAAC,SAAS;oCACzC,UAAU,aAAa,CAAC,IAAI,MAAM,SAAS;wCAAE,SAAS;oCAAK;oCAC3D,UAAU,aAAa,CAAC,IAAI,MAAM,UAAU;wCAAE,SAAS;oCAAK;oCAC5D,QAAQ,GAAG,CAAC;gCACd;4BACF,GAAG;wBACL,OACE,QAAQ,GAAG,CAAC;;oBAIhB,OAAO,SAAS,IAAI;gBACtB;gBAEA,yBAAyB;gBAEzB;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BC,GACD,aAAa,MAAM,IAAkB,EAA0B;oBAC7D,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAgB,eAAe;oBAErE,YAAY;oBACZ,IAAI,SAAS,IAAI,CAAC,KAAK,EACrB,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;oBAG3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;;;;;;GAmBC,GACD,aAAa,eAAuC;oBAClD,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC;oBAGF,UAAU;oBACV,IAAI,SAAS,IAAI,CAAC,KAAK,EACrB,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;oBAG3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;;;;;;;;;;;;;GAcC,GACD,aAAa,SAAwB;oBACnC,IAAI;wBACF,MAAM,mBAAU,CAAC,IAAI,CAAO;oBAC9B,SAAU;wBACR,uBAAuB;wBACvB,qBAAY,CAAC,UAAU;oBACzB;gBACF;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,aAAa,gBAAkC;oBAC7C,IAAI;wBACF,MAAM,WAAW,MAAM,mBAAU,CAAC,GAAG,CAAU;wBAC/C,OAAO,SAAS,IAAI;oBACtB,EAAE,OAAM;wBACN,OAAO;oBACT;gBACF;gBAEA;;;;;;;;;;;;;;;;GAgBC,GACD,OAAO,aAAsB;oBAC3B,OAAO,qBAAY,CAAC,QAAQ;gBAC9B;gBAEA;;;;;;;;;;;;;;GAcC,GACD,OAAO,WAA0B;oBAC/B,OAAO,qBAAY,CAAC,QAAQ;gBAC9B;gBAEA;;;;;;;;;;;GAWC,GACD,OAAO,aAAmB;oBACxB,qBAAY,CAAC,UAAU;gBACzB;gBAEA;;GAEC,GACD,OAAO,iBAAuB;oBAC5B,oBAAoB;oBACpB,qBAAY,CAAC,UAAU;gBACzB;gBAEA;;GAEC,GACD,aAAa,WAAW,IAAwB,EAA0B;oBACxE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,qBACA;oBAGF,UAAU;oBACV,IAAI,SAAS,IAAI,CAAC,KAAK,EACrB,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;oBAG3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,WAAW,IAAwB,EAA0B;oBACxE,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CACpC,qBACA;oBAGF,UAAU;oBACV,IAAI,SAAS,IAAI,CAAC,KAAK,EACrB,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;oBAG3C,OAAO,SAAS,IAAI;gBACtB;gBAEA;;GAEC,GACD,aAAa,YAA6B;oBACxC,MAAM,WAAW,MAAM,mBAAU,CAAC,IAAI,CAAS;oBAE/C,UAAU;oBACV,IAAI,SAAS,IAAI,EACf,qBAAY,CAAC,QAAQ,CAAC,SAAS,IAAI;oBAGrC,OAAO,SAAS,IAAI;gBACtB;gBAEA,8BAA8B;gBAE9B;;;GAGC,GACD,OAAO,kBAA2B;oBAChC,OAAO,YAAY,UAAU;gBAC/B;gBACA;;;GAGC,GACD,aAAa,UAAU,IAAwB,EAA0B;oBACvE,OAAO,YAAY,UAAU,CAAC;gBAChC;gBAEA;;;GAGC,GACD,OAAO,cAAoB;oBACzB,YAAY,UAAU;gBACxB;YACF;gBAGA,WAAe;;;;;;;;;;;;;;;;;;;;;;;IDlVD;AACZ,GACA,SAAU,OAAO;IACf,QAAQ,EAAE,GAAC;IACf,QAAQ,gBAAgB,CAAC;QAAC,gCAA+B;YAAC;SAA+B;QAAC,qCAAoC;YAAC;YAAU;SAAoC;QAAC,qBAAoB;YAAC;SAAS;QAAC,iCAAgC;YAAC;SAAsB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,gCAA+B;YAAC;YAAS;SAAmB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,oCAAmC;YAAC;SAAyB;QAAC,uCAAsC;YAAC;YAAS;SAAsC;QAAC,mCAAkC;YAAC;SAAyB;QAAC,4BAA2B;YAAC;YAAS;SAAiB;QAAC,4BAA2B;YAAC;SAAiB;QAAC,wCAAuC;YAAC;YAAS;SAA8B;QAAC,kCAAiC;YAAC;YAAS;SAAwB;IAAA;;AAC/4B"}