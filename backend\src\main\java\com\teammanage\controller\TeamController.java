package com.teammanage.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.teammanage.common.ApiResponse;
import com.teammanage.dto.request.CreateTeamRequest;
import com.teammanage.dto.request.InviteMembersRequest;
import com.teammanage.dto.request.UpdateTeamRequest;
import com.teammanage.dto.response.PageResponse;
import com.teammanage.dto.response.TeamDetailResponse;
import com.teammanage.dto.response.TeamMemberResponse;
import com.teammanage.service.TeamService;
import com.teammanage.util.SecurityUtil;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * 团队控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/teams")
@Tag(name = "团队管理", description = "团队管理相关接口")
@SecurityRequirement(name = "bearerAuth")
public class TeamController {

    private static final Logger log = LoggerFactory.getLogger(TeamController.class);

    @Autowired
    private TeamService teamService;

    /**
     * 创建团队（需要Account Token）
     */
    @PostMapping
    @Operation(summary = "创建团队", description = "创建新团队")
    public ApiResponse<TeamDetailResponse> createTeam(@Valid @RequestBody CreateTeamRequest request) {
        Long creatorId = SecurityUtil.getCurrentUserId();
        TeamDetailResponse response = teamService.createTeam(request, creatorId);
        return ApiResponse.success("团队创建成功", response);
    }

    /**
     * 获取用户的团队列表（需要Account Token）
     */
    @GetMapping
    @Operation(summary = "获取团队列表", description = "获取当前用户参与的所有团队")
    public ApiResponse<List<TeamDetailResponse>> getUserTeams(
            @RequestParam(value = "includeStats", required = false, defaultValue = "false") String includeStats) {
        Long userId = SecurityUtil.getCurrentUserId();
        List<TeamDetailResponse> response = teamService.getUserTeams(userId);

        // 如果请求参数包含includeStats=true，则添加统计数据
        if ("true".equals(includeStats)) {
            // 为每个团队添加模拟统计数据
            for (TeamDetailResponse team : response) {
                Map<String, Object> stats = new HashMap<>();
                // 根据团队ID生成不同的模拟数据
                if (team.getId() == 1) {
                    stats.put("vehicles", 120);
                    stats.put("personnel", 15);
                    stats.put("expiring", 5);
                    stats.put("overdue", 3);
                } else if (team.getId() == 2) {
                    stats.put("vehicles", 8);
                    stats.put("personnel", 3);
                    stats.put("expiring", 0);
                    stats.put("overdue", 0);
                } else {
                    // 其他团队的默认统计数据
                    stats.put("vehicles", (int)(Math.random() * 50));
                    stats.put("personnel", (int)(Math.random() * 20));
                    stats.put("expiring", (int)(Math.random() * 10));
                    stats.put("overdue", (int)(Math.random() * 5));
                }
                team.setStats(stats);
            }
        }

        return ApiResponse.success(response);
    }

    /**
     * 获取当前团队详情
     */
    @GetMapping("/current")
    @Operation(summary = "获取当前团队详情", description = "获取当前团队的详细信息")
    public ApiResponse<TeamDetailResponse> getCurrentTeamDetail() {
        TeamDetailResponse response = teamService.getCurrentTeamDetail();
        return ApiResponse.success(response);
    }

    /**
     * 更新当前团队信息（需要Team Token，仅创建者）
     */
    @PutMapping("/current")
    @Operation(summary = "更新团队信息", description = "更新当前团队信息")
    public ApiResponse<TeamDetailResponse> updateCurrentTeam(@Valid @RequestBody UpdateTeamRequest request) {
        TeamDetailResponse response = teamService.updateTeam(request);
        return ApiResponse.success("团队信息更新成功", response);
    }

    /**
     * 更新当前团队信息
     * @deprecated 使用 PUT /current 替代
     */
    @Deprecated
    @PostMapping("/current/update")
    @Operation(summary = "更新团队信息", description = "更新当前团队信息（已废弃，请使用PUT /current）")
    public ApiResponse<TeamDetailResponse> updateCurrentTeamLegacy(@Valid @RequestBody UpdateTeamRequest request) {
        TeamDetailResponse response = teamService.updateTeam(request);
        return ApiResponse.success("团队信息更新成功", response);
    }

    /**
     * 删除当前团队（需要Team Token，仅创建者）
     *
     * 接口功能：
     * - 软删除当前团队及所有相关数据
     * - 级联删除所有团队成员关系
     * - 记录删除操作日志
     *
     * 权限控制：
     * - 需要有效的Team Token（通过请求头传递）
     * - 只有团队创建者可以执行删除操作
     * - 双重权限验证确保安全性
     *
     * 删除效果：
     * - 团队记录标记为已删除（is_deleted=true）
     * - 所有成员关系失效（is_active=false, is_deleted=true）
     * - 数据不会物理删除，支持审计追踪
     *
     * 注意事项：
     * - 此操作不可逆，请谨慎使用
     * - 删除后团队Token将失效
     * - 所有成员将失去团队访问权限
     */
    @DeleteMapping("/current")
    @Operation(summary = "删除团队", description = "删除当前团队仅创建者可操作。删除后会级联删除所有团队成员关系")
    public ApiResponse<String> deleteCurrentTeam() {
        teamService.deleteTeam();
        return ApiResponse.success("团队删除成功");
    }

    /**
     * 获取当前团队成员列表（需要Team Token）
     */
    @GetMapping("/current/members")
    @Operation(summary = "获取团队成员", description = "获取当前团队的成员列表")
    public ApiResponse<PageResponse<TeamMemberResponse>> getTeamMembers(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int size) {
        Page<TeamMemberResponse> pageResult = teamService.getTeamMembers(page, size);
        PageResponse<TeamMemberResponse> response = PageResponse.fromPage(pageResult);
        return ApiResponse.success(response);
    }

    /**
     * 邀请团队成员（需要Team Token，仅创建者）
     */
    @PostMapping("/current/members/invite")
    @Operation(summary = "邀请团队成员", description = "邀请新成员加入当前团队仅创建者可操作")
    public ApiResponse<Void> inviteMembers(@Valid @RequestBody InviteMembersRequest request) {
        teamService.inviteMembers(request);
        return ApiResponse.<Void>success("成员邀请成功", null);
    }

    /**
     * 移除团队成员（需要Team Token，仅创建者）
     */
    @DeleteMapping("/current/members/{memberId}")
    @Operation(summary = "移除团队成员", description = "从当前团队移除指定成员仅创建者可操作")
    public ApiResponse<Void> removeMember(
            @Parameter(description = "成员ID") @PathVariable Long memberId) {
        teamService.removeMember(memberId);
        return ApiResponse.<Void>success("成员移除成功", null);
    }

    /**
     * 更新团队成员状态（需要Team Token，仅创建者）
     */
    @PutMapping("/current/members/{memberId}/status")
    @Operation(summary = "更新团队成员状态", description = "更新团队成员的启用/停用状态仅创建者可操作")
    public ApiResponse<Void> updateMemberStatus(
            @Parameter(description = "成员ID") @PathVariable Long memberId,
            @Parameter(description = "是否启用") @RequestParam Boolean isActive) {
        teamService.updateMemberStatus(memberId, isActive);
        return ApiResponse.<Void>success("成员状态更新成功", null);
    }

    /**
     * 获取当前团队的邀请列表（需要Team Token，仅创建者）
     */
    @GetMapping("/current/invitations")
    @Operation(summary = "获取当前团队邀请列表", description = "获取当前团队的所有邀请记录仅创建者可操作")
    public ApiResponse<List<com.teammanage.dto.response.TeamInvitationResponse>> getCurrentTeamInvitations() {
        Long teamId = com.teammanage.context.TeamContextHolder.getCurrentTeamId();
        List<com.teammanage.dto.response.TeamInvitationResponse> invitations =
            teamService.getTeamInvitations(teamId);
        return ApiResponse.success("获取团队邀请列表成功", invitations);
    }





}
